{"version": 3, "file": "no-var-requires.js", "sourceRoot": "", "sources": ["../../src/rules/no-var-requires.ts"], "names": [], "mappings": ";;AAEA,oDAAoE;AAEpE,kCAA2D;AAS3D,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,UAAU,EAAE,IAAI;QAChB,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;SACzE;QACD,QAAQ,EAAE;YACR,SAAS,EAAE,iDAAiD;SAC7D;QACD,UAAU,EAAE,CAAC,uCAAuC,CAAC;QACrD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,mDAAmD;wBAChE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAC1B;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,OAAO;QACrB,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CACxC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CACpC,CAAC;QACF,SAAS,mBAAmB,CAAC,UAAkB;YAC7C,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,yBAAyB,CAAC,IAAmB;YACpD,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACnC,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;gBACjC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAC7C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,uCAAuC,CACrC,IAA6B;gBAE7B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtE,MAAM,QAAQ,GAAG,IAAA,2BAAoB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzD,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAClE,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACjD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;oBACpB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAElB,IACE;oBACE,sBAAc,CAAC,cAAc;oBAC7B,sBAAc,CAAC,gBAAgB;oBAC/B,sBAAc,CAAC,aAAa;oBAC5B,sBAAc,CAAC,cAAc;oBAC7B,sBAAc,CAAC,eAAe;oBAC9B,sBAAc,CAAC,kBAAkB;iBAClC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EACvB,CAAC;oBACD,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CACpC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EACjC,SAAS,CACV,CAAC;oBAEF,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;wBAClC,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,WAAW;yBACvB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}