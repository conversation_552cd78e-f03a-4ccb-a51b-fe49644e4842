# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# IMPORTANT: Generate a new SECRET_KEY for production!
# You can generate one at: https://djecrety.ir/

# Django Core Settings
SECRET_KEY=your-super-secret-key-here-generate-a-new-one-for-production
DEBUG=False
ENVIRONMENT=production
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,localhost,127.0.0.1

# Database Configuration (PostgreSQL recommended for production)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=prestige_production
DB_USER=prestige_user
DB_PASSWORD=your_secure_database_password_here
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here
DEFAULT_FROM_EMAIL=Prestige Design and Builders <<EMAIL>>

# CORS and CSRF Settings (Update with your actual domain)
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CSRF_TRUSTED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Admin Configuration
ADMIN_URL=secure-admin-panel/