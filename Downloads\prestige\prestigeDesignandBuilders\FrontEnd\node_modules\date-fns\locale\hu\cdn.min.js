var N=function(J){return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},N(J)},M=function(J,H){var U=Object.keys(J);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(J);H&&(Y=Y.filter(function(z){return Object.getOwnPropertyDescriptor(J,z).enumerable})),U.push.apply(U,Y)}return U},x=function(J){for(var H=1;H<arguments.length;H++){var U=arguments[H]!=null?arguments[H]:{};H%2?M(Object(U),!0).forEach(function(Y){H1(J,Y,U[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(U)):M(Object(U)).forEach(function(Y){Object.defineProperty(J,Y,Object.getOwnPropertyDescriptor(U,Y))})}return J},H1=function(J,H,U){if(H=J1(H),H in J)Object.defineProperty(J,H,{value:U,enumerable:!0,configurable:!0,writable:!0});else J[H]=U;return J},J1=function(J){var H=U1(J,"string");return N(H)=="symbol"?H:String(H)},U1=function(J,H){if(N(J)!="object"||!J)return J;var U=J[Symbol.toPrimitive];if(U!==void 0){var Y=U.call(J,H||"default");if(N(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,U=function I(C,G){for(var B in G)H(C,B,{get:G[B],enumerable:!0,configurable:!0,set:function X(Z){return G[B]=function(){return Z}}})},Y={about:"k\xF6r\xFClbel\xFCl",over:"t\xF6bb mint",almost:"majdnem",lessthan:"kevesebb mint"},z={xseconds:" m\xE1sodperc",halfaminute:"f\xE9l perc",xminutes:" perc",xhours:" \xF3ra",xdays:" nap",xweeks:" h\xE9t",xmonths:" h\xF3nap",xyears:" \xE9v"},V={xseconds:{"-1":" m\xE1sodperccel ezel\u0151tt",1:" m\xE1sodperc m\xFAlva",0:" m\xE1sodperce"},halfaminute:{"-1":"f\xE9l perccel ezel\u0151tt",1:"f\xE9l perc m\xFAlva",0:"f\xE9l perce"},xminutes:{"-1":" perccel ezel\u0151tt",1:" perc m\xFAlva",0:" perce"},xhours:{"-1":" \xF3r\xE1val ezel\u0151tt",1:" \xF3ra m\xFAlva",0:" \xF3r\xE1ja"},xdays:{"-1":" nappal ezel\u0151tt",1:" nap m\xFAlva",0:" napja"},xweeks:{"-1":" h\xE9ttel ezel\u0151tt",1:" h\xE9t m\xFAlva",0:" hete"},xmonths:{"-1":" h\xF3nappal ezel\u0151tt",1:" h\xF3nap m\xFAlva",0:" h\xF3napja"},xyears:{"-1":" \xE9vvel ezel\u0151tt",1:" \xE9v m\xFAlva",0:" \xE9ve"}},R=function I(C,G,B){var X=C.match(/about|over|almost|lessthan/i),Z=X?C.replace(X[0],""):C,T=(B===null||B===void 0?void 0:B.addSuffix)===!0,E=Z.toLowerCase(),q=(B===null||B===void 0?void 0:B.comparison)||0,O=T?V[E][q]:z[E],D=E==="halfaminute"?O:G+O;if(X){var K=X[0].toLowerCase();D=Y[K]+" "+D}return D};function W(I){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):I.defaultWidth,B=I.formats[G]||I.formats[I.defaultWidth];return B}}var S={full:"y. MMMM d., EEEE",long:"y. MMMM d.",medium:"y. MMM d.",short:"y. MM. dd."},L={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},j={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},P={date:W({formats:S,defaultWidth:"full"}),time:W({formats:L,defaultWidth:"full"}),dateTime:W({formats:j,defaultWidth:"full"})},$=function I(C){return function(G){var B=_[G.getDay()],X=C?"":"'m\xFAlt' ";return"".concat(X,"'").concat(B,"' p'-kor'")}},_=["vas\xE1rnap","h\xE9tf\u0151n","kedden","szerd\xE1n","cs\xFCt\xF6rt\xF6k\xF6n","p\xE9nteken","szombaton"],f={lastWeek:$(!1),yesterday:"'tegnap' p'-kor'",today:"'ma' p'-kor'",tomorrow:"'holnap' p'-kor'",nextWeek:$(!0),other:"P"},v=function I(C,G){var B=f[C];if(typeof B==="function")return B(G);return B};function A(I){return function(C,G){var B=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(B==="formatting"&&I.formattingValues){var Z=I.defaultFormattingWidth||I.defaultWidth,T=G!==null&&G!==void 0&&G.width?String(G.width):Z;X=I.formattingValues[T]||I.formattingValues[Z]}else{var E=I.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):I.defaultWidth;X=I.values[q]||I.values[E]}var O=I.argumentCallback?I.argumentCallback(C):C;return X[O]}}var F={narrow:["ie.","isz."],abbreviated:["i. e.","i. sz."],wide:["Krisztus el\u0151tt","id\u0151sz\xE1m\xEDt\xE1sunk szerint"]},w={narrow:["1.","2.","3.","4."],abbreviated:["1. n.\xE9v","2. n.\xE9v","3. n.\xE9v","4. n.\xE9v"],wide:["1. negyed\xE9v","2. negyed\xE9v","3. negyed\xE9v","4. negyed\xE9v"]},b={narrow:["I.","II.","III.","IV."],abbreviated:["I. n.\xE9v","II. n.\xE9v","III. n.\xE9v","IV. n.\xE9v"],wide:["I. negyed\xE9v","II. negyed\xE9v","III. negyed\xE9v","IV. negyed\xE9v"]},h={narrow:["J","F","M","\xC1","M","J","J","A","Sz","O","N","D"],abbreviated:["jan.","febr.","m\xE1rc.","\xE1pr.","m\xE1j.","j\xFAn.","j\xFAl.","aug.","szept.","okt.","nov.","dec."],wide:["janu\xE1r","febru\xE1r","m\xE1rcius","\xE1prilis","m\xE1jus","j\xFAnius","j\xFAlius","augusztus","szeptember","okt\xF3ber","november","december"]},c={narrow:["V","H","K","Sz","Cs","P","Sz"],short:["V","H","K","Sze","Cs","P","Szo"],abbreviated:["V","H","K","Sze","Cs","P","Szo"],wide:["vas\xE1rnap","h\xE9tf\u0151","kedd","szerda","cs\xFCt\xF6rt\xF6k","p\xE9ntek","szombat"]},k={narrow:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"du.",evening:"este",night:"\xE9jjel"},abbreviated:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"du.",evening:"este",night:"\xE9jjel"},wide:{am:"de.",pm:"du.",midnight:"\xE9jf\xE9l",noon:"d\xE9l",morning:"reggel",afternoon:"d\xE9lut\xE1n",evening:"este",night:"\xE9jjel"}},m=function I(C,G){var B=Number(C);return B+"."},y={ordinalNumber:m,era:A({values:F,defaultWidth:"wide"}),quarter:A({values:w,defaultWidth:"wide",argumentCallback:function I(C){return C-1},formattingValues:b,defaultFormattingWidth:"wide"}),month:A({values:h,defaultWidth:"wide"}),day:A({values:c,defaultWidth:"wide"}),dayPeriod:A({values:k,defaultWidth:"wide"})};function Q(I){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.width,X=B&&I.matchPatterns[B]||I.matchPatterns[I.defaultMatchWidth],Z=C.match(X);if(!Z)return null;var T=Z[0],E=B&&I.parsePatterns[B]||I.parsePatterns[I.defaultParseWidth],q=Array.isArray(E)?g(E,function(K){return K.test(T)}):p(E,function(K){return K.test(T)}),O;O=I.valueCallback?I.valueCallback(q):q,O=G.valueCallback?G.valueCallback(O):O;var D=C.slice(T.length);return{value:O,rest:D}}}var p=function I(C,G){for(var B in C)if(Object.prototype.hasOwnProperty.call(C,B)&&G(C[B]))return B;return},g=function I(C,G){for(var B=0;B<C.length;B++)if(G(C[B]))return B;return};function u(I){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.match(I.matchPattern);if(!B)return null;var X=B[0],Z=C.match(I.parsePattern);if(!Z)return null;var T=I.valueCallback?I.valueCallback(Z[0]):Z[0];T=G.valueCallback?G.valueCallback(T):T;var E=C.slice(X.length);return{value:T,rest:E}}}var l=/^(\d+)\.?/i,d=/\d+/i,i={narrow:/^(ie\.|isz\.)/i,abbreviated:/^(i\.\s?e\.?|b?\s?c\s?e|i\.\s?sz\.?)/i,wide:/^(Krisztus előtt|időszámításunk előtt|időszámításunk szerint|i\. sz\.)/i},n={narrow:[/ie/i,/isz/i],abbreviated:[/^(i\.?\s?e\.?|b\s?ce)/i,/^(i\.?\s?sz\.?|c\s?e)/i],any:[/előtt/i,/(szerint|i. sz.)/i]},s={narrow:/^[1234]\.?/i,abbreviated:/^[1234]?\.?\s?n\.év/i,wide:/^([1234]|I|II|III|IV)?\.?\s?negyedév/i},o={any:[/1|I$/i,/2|II$/i,/3|III/i,/4|IV/i]},r={narrow:/^[jfmaásond]|sz/i,abbreviated:/^(jan\.?|febr\.?|márc\.?|ápr\.?|máj\.?|jún\.?|júl\.?|aug\.?|szept\.?|okt\.?|nov\.?|dec\.?)/i,wide:/^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i},a={narrow:[/^j/i,/^f/i,/^m/i,/^a|á/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s|sz/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^már/i,/^áp/i,/^máj/i,/^jún/i,/^júl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},e={narrow:/^([vhkpc]|sz|cs|sz)/i,short:/^([vhkp]|sze|cs|szo)/i,abbreviated:/^([vhkp]|sze|cs|szo)/i,wide:/^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i},t={narrow:[/^v/i,/^h/i,/^k/i,/^sz/i,/^c/i,/^p/i,/^sz/i],any:[/^v/i,/^h/i,/^k/i,/^sze/i,/^c/i,/^p/i,/^szo/i]},I1={any:/^((de|du)\.?|éjfél|délután|dél|reggel|este|éjjel)/i},B1={any:{am:/^de\.?/i,pm:/^du\.?/i,midnight:/^éjf/i,noon:/^dé/i,morning:/reg/i,afternoon:/^délu\.?/i,evening:/es/i,night:/éjj/i}},C1={ordinalNumber:u({matchPattern:l,parsePattern:d,valueCallback:function I(C){return parseInt(C,10)}}),era:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),quarter:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any",valueCallback:function I(C){return C+1}}),month:Q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),day:Q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:I1,defaultMatchWidth:"any",parsePatterns:B1,defaultParseWidth:"any"})},G1={code:"hu",formatDistance:R,formatLong:P,formatRelative:v,localize:y,match:C1,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{hu:G1})})})();

//# debugId=E44515F7CF09B65664756e2164756e21
