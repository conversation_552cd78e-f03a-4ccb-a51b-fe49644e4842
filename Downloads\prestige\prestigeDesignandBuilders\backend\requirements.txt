# =============================================================================
# CPANEL-OPTIMIZED REQUIREMENTS
# =============================================================================

# Core Django packages
Django==5.1.1
djangorestframework==3.14.0
django-cors-headers==4.3.1

# Database drivers
# For MySQL/MariaDB (most common on cPanel)
mysqlclient==2.2.0
# Alternative MySQL driver if mysqlclient fails
# PyMySQL==1.1.0

# For PostgreSQL (if your cPanel supports it)
# psycopg2-binary==2.9.7

# Static files and media
whitenoise==6.6.0
Pillow==10.0.1

# Environment management
python-dotenv==1.0.0

# Security (lightweight versions for cPanel)
django-ratelimit==4.1.0

# Production server (may not be needed on cPanel)
# gunicorn==21.2.0

# Essential packages only for cPanel
requests==2.31.0
urllib3==2.0.7

# Development tools (install only in development)
# django-debug-toolbar==4.2.0
# django-extensions==3.2.3

# =============================================================================
# INSTALLATION NOTES FOR CPANEL:
# =============================================================================
# 1. Some packages may require compilation and might not work on shared hosting
# 2. If mysqlclient fails, uncomment PyMySQL and add this to settings.py:
#    import pymysql
#    pymysql.install_as_MySQLdb()
# 3. Remove gunicorn if using cPanel's built-in Python app hosting
# 4. Install packages using: pip install -r requirements.txt --user
# =============================================================================
