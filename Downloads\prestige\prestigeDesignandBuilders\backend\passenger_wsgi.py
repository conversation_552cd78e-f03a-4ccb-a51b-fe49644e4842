#!/usr/bin/env python3
"""
Passenger WSGI configuration for cPanel hosting
This file is used by Passenger to serve the Django application
"""
import os
import sys

# Get the current directory (where this file is located)
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# Add the project directory to Python path
sys.path.insert(0, CURRENT_DIR)

# Add the parent directory (in case backend is in a subdirectory)
sys.path.insert(0, os.path.dirname(CURRENT_DIR))

# Set environment variables for cPanel
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = os.path.join(CURRENT_DIR, '.env')
    if os.path.exists(env_path):
        load_dotenv(env_path)
except ImportError:
    # dotenv not available, skip loading
    pass

# Import Django WSGI application
from django.core.wsgi import get_wsgi_application

# Create the WSGI application
application = get_wsgi_application()
